"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { motion } from "framer-motion";
import {
  Terminal,
  Brain,
  TrendingUp,
  ShoppingBag,
  UserCheck,
  Kanban,
  Globe,
  Loader2,

} from "lucide-react";
import { toast } from "sonner";

// Import actual AdMesh UI SDK components
// Using components that integrate cleanly within preview containers
import {
  AdMeshProductCard,
  AdMeshConversationSummary,
  AdMeshInlineRecommendation,
  AdMeshCitationUnit,
  AdMeshSidebar
} from "admesh-ui-sdk";

interface PlatformCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  recommendedFormat: string; // Single format instead of array
  examples: string[];
}

interface BrandData {
  title: string;
  description: string;
  company_name: string;
  url: string;
  pricing: string;
  categories: string[];
  keywords: string[];
  features: string[];
  industry: string;
  target_audience: string;
  value_proposition: string;
  pricing_model: string;
  has_free_tier: boolean;
  trial_days?: number;
  trust_indicators: string[];
}

interface SuggestedWebsite {
  slug: string;
  url: string;
  brandData: BrandData;
}

// Pre-cached brand data for suggested websites
const SUGGESTED_WEBSITES: SuggestedWebsite[] = [
  {
    slug: "shopify.com",
    url: "https://shopify.com",
    brandData: {
      title: "Shopify",
      description: "Complete commerce platform that lets you start, grow, and manage a business.",
      company_name: "Shopify",
      url: "https://shopify.com",
      pricing: "Starting at $29/month",
      categories: ["E-commerce", "SaaS", "Business Tools"],
      keywords: ["ecommerce", "online store", "selling", "business", "commerce"],
      features: ["Online Store", "Payment Processing", "Inventory Management", "Marketing Tools", "Analytics"],
      industry: "E-commerce Technology",
      target_audience: "Small to large businesses wanting to sell online",
      value_proposition: "Everything you need to start, sell, market and manage your business",
      pricing_model: "Monthly subscription",
      has_free_tier: true,
      trial_days: 14,
      trust_indicators: ["Trusted by millions", "24/7 support", "99.9% uptime"]
    }
  },
  {
    slug: "notion.so",
    url: "https://notion.so",
    brandData: {
      title: "Notion",
      description: "One workspace. Every team. We're more than a doc. Or a table. Customize Notion to work the way you do.",
      company_name: "Notion",
      url: "https://notion.so",
      pricing: "Free for personal use, $8/month per user for teams",
      categories: ["Productivity", "Collaboration", "Documentation"],
      keywords: ["workspace", "notes", "collaboration", "productivity", "documentation"],
      features: ["Notes & Docs", "Wikis", "Projects", "Databases", "AI Assistant"],
      industry: "Productivity Software",
      target_audience: "Teams, students, and individuals seeking organized workspace",
      value_proposition: "Write, plan, share, and get organized. All in one workspace.",
      pricing_model: "Freemium with paid plans",
      has_free_tier: true,
      trial_days: undefined,
      trust_indicators: ["Used by millions", "Enterprise security", "24/7 support"]
    }
  },
  {
    slug: "stripe.com",
    url: "https://stripe.com",
    brandData: {
      title: "Stripe",
      description: "Financial infrastructure for the internet. Millions of businesses rely on Stripe's software and APIs to accept payments.",
      company_name: "Stripe",
      url: "https://stripe.com",
      pricing: "2.9% + 30¢ per transaction",
      categories: ["Fintech", "Payment Processing", "Developer Tools"],
      keywords: ["payments", "fintech", "api", "transactions", "billing"],
      features: ["Payment Processing", "Billing", "Connect", "Radar", "Terminal"],
      industry: "Financial Technology",
      target_audience: "Businesses of all sizes needing payment solutions",
      value_proposition: "Increase revenue and lower costs with a complete payments platform",
      pricing_model: "Pay-per-transaction",
      has_free_tier: false,
      trial_days: undefined,
      trust_indicators: ["SOC 2 certified", "PCI compliant", "99.99% uptime"]
    }
  }
];

export default function InteractiveBrandDemo() {
  const [websiteUrl, setWebsiteUrl] = useState<string>("");
  const [selectedCategory, setSelectedCategory] = useState<string>("ai-code-editors");
  const [brandData, setBrandData] = useState<BrandData | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);
  const [analysisError, setAnalysisError] = useState<string>("");
  const [showSuggestions, setShowSuggestions] = useState<boolean>(true);


  const platformCategories: PlatformCategory[] = [
    {
      id: "ai-code-editors",
      name: "AI Code Editors",
      description: "Development environments and coding assistants",
      icon: <Terminal className="w-5 h-5" />,
      recommendedFormat: "one-line-ad",
      examples: ["Cursor", "Sweep", "Mutable AI"]
    },
    {
      id: "ai-design-tools",
      name: "AI Design Tools",
      description: "Creative workflows and design platforms",
      icon: <Brain className="w-5 h-5" />,
      recommendedFormat: "product-card",
      examples: ["Uizard", "Galileo AI", "Khroma"]
    },
    {
      id: "ai-business-intelligence",
      name: "AI Business Intelligence",
      description: "Analytics and data visualization platforms",
      icon: <TrendingUp className="w-5 h-5" />,
      recommendedFormat: "citation",
      examples: ["Polymer", "Yurts AI", "Obviously.AI"]
    },
    {
      id: "ai-ecommerce",
      name: "AI E-commerce Platforms",
      description: "Online store optimization and conversion tools",
      icon: <ShoppingBag className="w-5 h-5" />,
      recommendedFormat: "floating-recommendations",
      examples: ["Octane AI", "ConvertMate AI", "Checkout Page AI"]
    },
    {
      id: "ai-crm",
      name: "AI CRM Systems",
      description: "Customer relationship and sales automation",
      icon: <UserCheck className="w-5 h-5" />,
      recommendedFormat: "conversation-summary",
      examples: ["Regie.ai", "CoPilot AI", "Nooks"]
    },
    {
      id: "ai-project-management",
      name: "AI Project Management",
      description: "Productivity and collaboration platforms",
      icon: <Kanban className="w-5 h-5" />,
      recommendedFormat: "sidebar",
      examples: ["Taskade AI", "Tability AI", "Magical"]
    }
  ];

  // Function to analyze website and extract brand data
  const analyzeBrand = async (website: string): Promise<BrandData | null> => {
    try {
      setIsAnalyzing(true);
      setAnalysisError("");

      const response = await fetch('/api/brand-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ website }),
      });

      if (!response.ok) {
        throw new Error('Failed to analyze website');
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      return data;
    } catch (error) {
      console.error('Brand analysis error:', error);
      setAnalysisError(error instanceof Error ? error.message : 'Failed to analyze website');
      return null;
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Function to convert brand data to AdMesh recommendation format
  const brandToRecommendation = (brand: BrandData) => ({
    title: brand.title,
    reason: brand.value_proposition,
    intent_match_score: 0.95,
    admesh_link: `${brand.url}?ref=admesh`,
    ad_id: "demo_ad_001",
    product_id: "demo_prod_001",
    url: brand.url,
    description: brand.description,
    pricing: brand.pricing,
    keywords: brand.keywords,
    categories: brand.categories,
    features: brand.features,
    has_free_tier: brand.has_free_tier,
    trial_days: brand.trial_days || undefined,
    is_ai_powered: true,
    offer_trust_score: 0.92,
    brand_trust_score: 0.88
  });

  // Handle website preview for custom URLs
  const handleAnalyzeWebsite = async () => {
    if (!websiteUrl.trim()) {
      toast.error("Please enter a website URL");
      return;
    }

    // Hide suggestions when preview is clicked
    setShowSuggestions(false);

    // Check if this is a suggested website (use cached data)
    const suggestedWebsite = SUGGESTED_WEBSITES.find(site => site.url === websiteUrl);
    if (suggestedWebsite) {
      setBrandData(suggestedWebsite.brandData);
      return;
    }

    // For custom URLs, use API analysis
    const analyzed = await analyzeBrand(websiteUrl);
    if (analyzed) {
      setBrandData(analyzed);
    } else {
      toast.error("Failed to analyze website. Please try again.");
    }
  };

  // Handle suggested website click (uses cached data)
  const handleSuggestedWebsiteClick = (website: SuggestedWebsite) => {
    setWebsiteUrl(website.url);
    setBrandData(website.brandData);
    setAnalysisError("");
    setShowSuggestions(false);
  };

  // Generate ad format component based on category and brand data
  const getAdFormatComponent = (categoryId: string) => {
    const category = platformCategories.find(cat => cat.id === categoryId);
    if (!category || !brandData) return null;

    const recommendation = brandToRecommendation(brandData);
    const formatId = category.recommendedFormat;

    switch (formatId) {
        case "one-line-ad":
          return (
            <div className="p-4 bg-white dark:bg-gray-800 rounded-lg border">
              <AdMeshInlineRecommendation
                recommendation={recommendation}
                compact={true}
                showReason={true}
                variation="statement"
              />
            </div>
          );
        case "product-card":
          return (
            <div className="p-4 bg-white dark:bg-gray-800 rounded-lg border">
              <AdMeshProductCard
                recommendation={recommendation}
                showMatchScore={true}
                showBadges={true}
              />
            </div>
          );
        case "citation":
          return (
            <div className="p-4 bg-white dark:bg-gray-800 rounded-lg border">
              {/*
                AdMeshCitationUnit: Perfect for educational/research platforms
                - Integrates naturally within content containers
                - Shows academic-style numbered citations
                - Easy to embed in existing text layouts
              */}
              <AdMeshCitationUnit
                recommendations={[recommendation]}
                conversationText={`Based on recent studies, the most effective ${brandData.industry.toLowerCase()} tools include several key features that enhance ${brandData.target_audience.toLowerCase()} productivity and collaboration.`}
                showCitationList={true}
                citationStyle="numbered"
                onRecommendationClick={(adId, admeshLink) => window.open(admeshLink, '_blank')}
                onCitationHover={(rec) => console.log('Citation hovered:', rec.title)}
              />
            </div>
          );
        case "sidebar":
          return (
            <div className="bg-white dark:bg-gray-800 rounded-lg border relative min-h-[400px] flex overflow-hidden">
              <div className="flex-1 p-4">
                <div className="text-sm text-gray-700 dark:text-gray-300 mb-4">
                  Your main application content appears here. The sidebar provides persistent access to recommendations.
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-1/2"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-2/3"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-5/6"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-1/3"></div>
                </div>

                {/* Mobile fallback - show inline recommendations instead of sidebar */}
                <div className="sm:hidden mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                    Recommendations
                  </h4>
                  <AdMeshInlineRecommendation
                    recommendation={recommendation}
                    compact={true}
                    showReason={true}
                    onClick={(adId: string, admeshLink: string) => window.open(admeshLink, '_blank')}
                  />
                </div>
              </div>

              {/*
                AdMeshSidebar with containerMode for demo integration
                - Uses the actual SDK component with containerMode=true
                - Perfect for dashboard and admin interfaces
                - Shows real sidebar functionality within preview container
                - Hidden on mobile screens (< 640px)
              */}
              <div className="hidden sm:block">
                <AdMeshSidebar
                  recommendations={[recommendation]}
                  config={{
                    position: 'right',
                    size: 'md',
                    displayMode: 'recommendations',
                    collapsible: true,
                    showHeader: true,
                    showSearch: false,
                    maxRecommendations: 2
                  }}
                  title="Recommendations"
                  isOpen={true}
                  containerMode={true}
                  onRecommendationClick={(adId: string, admeshLink: string) => window.open(admeshLink, '_blank')}
                  className="border-l-0"
                />
              </div>
            </div>
          );
        case "floating-recommendations":
          return (
            <div className="p-4 bg-white dark:bg-gray-800 rounded-lg border relative min-h-[250px]">
              <div className="text-sm text-gray-700 dark:text-gray-300 mb-4">
                Your main interface content appears here. The floating recommendation widget appears contextually based on user behavior.
              </div>
              <div className="space-y-2 mb-8">
                <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-1/2"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-2/3"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-5/6"></div>
              </div>

              {/*
                Custom floating widget using AdMeshInlineRecommendation
                - Shows how to create floating overlays with SDK components
                - AdMeshAutoRecommendationWidget is designed for full-page overlays
                - This approach keeps the widget contained within the preview
                - Perfect for contextual recommendations in content areas
              */}
              <div className="absolute bottom-4 right-4 w-72 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg">
                <div className="p-3 border-b border-gray-200 dark:border-gray-600">
                  <div className="flex items-center justify-between">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">Recommended for you</div>
                    <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                </div>
                <div className="p-3">
                  <AdMeshInlineRecommendation
                    recommendation={recommendation}
                    compact={true}
                    showReason={true}
                    onClick={(adId, admeshLink) => window.open(admeshLink, '_blank')}
                  />
                  <div className="mt-3 text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Powered by AdMesh
                  </div>
                </div>
              </div>
            </div>
          );
        case "conversation-summary":
          return (
            <div className="p-4 bg-white dark:bg-gray-800 rounded-lg border">
              <AdMeshConversationSummary
                recommendations={[recommendation]}
                conversationSummary={`Based on our conversation about ${brandData.industry.toLowerCase()} solutions, here are the top recommendations for your needs.`}
                showTopRecommendations={2}
              />
            </div>
          );
        default:
          return (
            <div className="p-4 bg-white dark:bg-gray-800 rounded-lg border">
              <div className="text-center text-gray-500 dark:text-gray-400">
                Format preview not available
              </div>
            </div>
          );
    }
  };

  const selectedCategoryData = platformCategories.find(cat => cat.id === selectedCategory);
  const adFormatComponent = selectedCategory && brandData ? getAdFormatComponent(selectedCategory) : null;



  return (
    <div className="w-full max-w-4xl mx-auto px-4 sm:px-0">
      <Card className="border border-gray-200 dark:border-gray-700 shadow-lg bg-white dark:bg-gray-800">
        <CardHeader className="text-center pb-3">
          <CardTitle className="flex items-center justify-center gap-2 text-lg text-gray-900 dark:text-white">
            <Globe className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            Interactive Brand Demo
          </CardTitle>
          <p className="text-gray-600 dark:text-gray-300 text-sm">
            Enter your website URL and select a platform category to see how your brand appears in realistic ad previews
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Website URL Input */}
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-900 dark:text-white">
                Enter Website URL
              </label>
            </div>

            {/* Custom URL Input */}
            <div className="flex flex-col sm:flex-row gap-2">
              <Input
                placeholder="https://yourbrand.com"
                value={websiteUrl}
                onChange={(e) => setWebsiteUrl(e.target.value)}
                className="flex-1 text-sm bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              />
              <Button
                onClick={handleAnalyzeWebsite}
                disabled={!websiteUrl.trim() || isAnalyzing}
                className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 sm:w-auto w-full text-sm text-white"
                size="sm"
              >
                {isAnalyzing ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Loading...
                  </>
                ) : (
                  'Preview'
                )}
              </Button>
            </div>
            {analysisError && (
              <p className="text-sm text-red-600 dark:text-red-400">{analysisError}</p>
            )}

            {/* Suggested Websites */}
            {showSuggestions && (
              <div className="space-y-2">
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Or try these examples:
                </p>
                <div className="flex flex-wrap gap-2">
                  {SUGGESTED_WEBSITES.map((website) => (
                    <Button
                      key={website.slug}
                      variant={websiteUrl === website.url ? "default" : "outline"}
                      onClick={() => handleSuggestedWebsiteClick(website)}
                      className={`h-7 px-2 text-xs transition-all duration-200 ${
                        websiteUrl === website.url
                          ? "bg-blue-600 hover:bg-blue-700 text-white border-blue-600"
                          : "hover:bg-gray-50 dark:hover:bg-gray-700"
                      }`}
                      size="sm"
                    >
                      {website.slug}
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Brand Analysis Results */}
          {/* {brandData && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700 rounded-lg p-4"
            >
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                  <Sparkles className="w-4 h-4 text-green-600 dark:text-green-400" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                    {brandData.company_name} - Analysis Complete
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                    {brandData.description}
                  </p>
                  <div className="grid grid-cols-2 gap-4 text-xs text-gray-500 dark:text-gray-400">
                    <div><strong>Industry:</strong> {brandData.industry}</div>
                    <div><strong>Pricing:</strong> {brandData.pricing}</div>
                    <div><strong>Target:</strong> {brandData.target_audience}</div>
                    <div><strong>Free Tier:</strong> {brandData.has_free_tier ? 'Yes' : 'No'}</div>
                  </div>
                </div>
              </div>
            </motion.div>
          )} */}

          {/* Platform Category Selector & Live Preview */}
          {brandData && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-3"
            >
              <div>
                <label className="text-sm font-medium text-gray-900 dark:text-white">
                  Choose Your Platform Category
                </label>
              </div>

              {/* Platform Category Selection - Always Visible */}
              <div className="space-y-2">
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 p-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800/50">
                  {platformCategories.map((category) => (
                    <Button
                      key={category.id}
                      variant={selectedCategory === category.id ? "default" : "outline"}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`h-auto p-2 flex flex-col items-center gap-1 text-center hover:shadow-sm transition-all ${
                        selectedCategory === category.id
                          ? "bg-blue-600 hover:bg-blue-700 text-white border-blue-600"
                          : "hover:bg-gray-50 dark:hover:bg-gray-700"
                      }`}
                      size="sm"
                    >
                      <div className={`w-6 h-6 rounded-md flex items-center justify-center flex-shrink-0 ${
                        selectedCategory === category.id
                          ? "bg-blue-500 text-white"
                          : "bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400"
                      }`}>
                        {category.icon}
                      </div>
                      <div className="text-xs font-medium truncate w-full leading-tight">
                        {category.name.replace('AI ', '')}
                      </div>
                    </Button>
                  ))}
                </div>
              </div>

              {/* Live Preview */}
              {selectedCategoryData && (
                <div className="space-y-2 mt-4">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                    Live Preview - {selectedCategoryData.name}
                  </h4>
                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-900/50">
                    {adFormatComponent}
                  </div>
                </div>
              )}
            </motion.div>
          )}
           <div className="pt-6 text-center flex flex-row items-center justify-center gap-2">
            <span className="text-base font-medium text-gray-900 dark:text-white">Ready to promote?</span>
            <a
              href="/auth/signin/?role=brand"
              className="inline-flex items-center gap-1 text-base font-semibold text-blue-700 dark:text-blue-400 hover:underline hover:text-blue-900 dark:hover:text-blue-200 transition"
            >
              <span className="underline decoration-dotted">Get Started</span>
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
