"use client";

import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Search,
  AlertCircle,
  Target,
  BarChart3,
  RefreshCw,
  Download,
  History,
  ArrowLeft
} from "lucide-react";
import { GEOScoreCard } from "@/components/geo-check/GEOScoreCard";
import { GEORecommendationCard } from "@/components/geo-check/GEORecommendationCard";
import { GEOAnalysisLoader } from "@/components/geo-check/GEOAnalysisLoader";
import { GEOHistoryList } from "@/components/geo-check/GEOHistoryList";
import { GEOHistoricalReport } from "@/components/geo-check/GEOHistoricalReport";

interface PageAnalysis {
  url: string;
  title: string;
  summary: string;
  score: number;
}

interface GEOAnalysis {
  overallScore: number;
  promptMentionRate: number;
  citationRate: number;
  websiteOptimization: number;
  sentimentTone: number;
  aiDiscoverability: {
    score: number;
    mentions: number;
    sentiment: "positive" | "neutral" | "negative";
    topQueries: string[];
  };
  contentOptimization: {
    score: number;
    structureScore: number;
    factualClaimsScore: number;
    aiReadabilityScore: number;
  };
  competitiveAnalysis: {
    shareOfVoice: number;
    competitorMentions: { name: string; mentions: number }[];
  };
  analyzedPages: PageAnalysis[];
  simulatedQueries: Array<{
    query: string;
    brand_mentioned: boolean;
    mention_context?: string;
    likelihood_score: number;
    reasoning: string;
  }>;
  recommendations: {
    priority: "high" | "medium" | "low";
    category: string;
    title: string;
    description: string;
    impact: string;
  }[];
  brandId: string;
}

interface GEOHistoryItem {
  id: string;
  created_at: string;
  overall_score: number;
  website_analyzed: string;
  brand_name: string;
  analyzed_pages_count: number;
  total_queries_simulated: number;
  prompt_mention_rate: number;
  citation_rate: number;
  website_optimization: number;
  sentiment_tone: number;
  analysis_version: string;
}

interface HistoricalGEOAnalysis {
  analysisId: string;
  overallScore: number;
  promptMentionRate: number;
  citationRate: number;
  websiteOptimization: number;
  sentimentTone: number;
  aiDiscoverability: {
    score: number;
    mentions: number;
    sentiment: "positive" | "neutral" | "negative";
    topQueries: string[];
  };
  contentOptimization: {
    score: number;
    structureScore: number;
    factualClaimsScore: number;
    aiReadabilityScore: number;
  };
  competitiveAnalysis: {
    shareOfVoice: number;
    competitorMentions: { name: string; mentions: number }[];
  };
  analyzedPages: Array<{
    url: string;
    title: string;
    summary: string;
    score: number;
  }>;
  simulatedQueries: Array<{
    query: string;
    brand_mentioned: boolean;
    mention_context?: string;
    likelihood_score: number;
    reasoning: string;
  }>;
  recommendations: Array<{
    priority: "high" | "medium" | "low";
    category: string;
    title: string;
    description: string;
    impact: string;
  }>;
  analyzedAt: string;
  brandId: string;
  websiteAnalyzed: string;
  brandName: string;
  isHistorical: boolean;
  analysisVersion: string;
}

export default function GEOCheckPage() {
  const { user } = useAuth();
  const [analysis, setAnalysis] = useState<GEOAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [brandData, setBrandData] = useState<{
    website?: string;
    company_name?: string;
    industry?: string;
  } | null>(null);

  // History state management
  const [currentView, setCurrentView] = useState<'analysis' | 'history' | 'historical-report'>('analysis');
  const [history, setHistory] = useState<GEOHistoryItem[]>([]);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [selectedHistoricalAnalysis, setSelectedHistoricalAnalysis] = useState<HistoricalGEOAnalysis | null>(null);
  const [historyPagination, setHistoryPagination] = useState({
    offset: 0,
    limit: 10,
    hasMore: false,
    total: 0
  });

  const fetchBrandData = useCallback(async () => {
    if (!user) return;

    try {
      const token = await user.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/brands/profile`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setBrandData(data);
      }
    } catch (error) {
      console.error("Error fetching brand data:", error);
    }
  }, [user]);

  useEffect(() => {
    fetchBrandData();
  }, [fetchBrandData]);

  // Fetch GEO analysis history
  const fetchHistory = useCallback(async (loadMore = false) => {
    if (!user) return;

    setHistoryLoading(true);
    try {
      const token = await user.getIdToken();
      const offset = loadMore ? historyPagination.offset + historyPagination.limit : 0;

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/geo/history?limit=${historyPagination.limit}&offset=${offset}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();

        if (loadMore) {
          setHistory(prev => [...prev, ...data.history]);
        } else {
          setHistory(data.history);
        }

        setHistoryPagination({
          offset: offset,
          limit: historyPagination.limit,
          hasMore: data.pagination.has_more,
          total: data.pagination.total
        });
      } else {
        console.error("Failed to fetch history");
      }
    } catch (error) {
      console.error("Error fetching history:", error);
    } finally {
      setHistoryLoading(false);
    }
  }, [user, historyPagination.limit, historyPagination.offset]);

  // Fetch specific historical analysis
  const fetchHistoricalAnalysis = useCallback(async (analysisId: string) => {
    if (!user) return;

    setLoading(true);
    try {
      const token = await user.getIdToken();
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/geo/history/${analysisId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setSelectedHistoricalAnalysis(data);
        setCurrentView('historical-report');
      } else {
        const errorData = await response.json().catch(() => ({}));
        alert(`Failed to load historical analysis: ${errorData.detail || 'Please try again.'}`);
      }
    } catch (error) {
      console.error("Error fetching historical analysis:", error);
      alert("Failed to load historical analysis. Please try again.");
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Load history when switching to history view
  useEffect(() => {
    if (currentView === 'history' && history.length === 0) {
      fetchHistory();
    }
  }, [currentView, history.length, fetchHistory]);

  const runGEOReport = async () => {
    if (!brandData?.website) {
      alert("Please add your website in the brand profile first.");
      return;
    }

    setLoading(true);
    try {
      const token = await user?.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/geo/check`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          brand_id: null // Will use authenticated user's brand
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setAnalysis(data);
        setCurrentView('analysis');

        // Refresh history to include the new analysis
        if (history.length > 0) {
          fetchHistory();
        }
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || "Failed to run GEO analysis");
      }
    } catch (error) {
      console.error("Error running GEO analysis:", error);
      alert(`Failed to run GEO analysis: ${error instanceof Error ? error.message : 'Please try again.'}`);
    } finally {
      setLoading(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return "default";
    if (score >= 60) return "secondary";
    return "destructive";
  };

  const exportToPDF = () => {
    if (!analysis) return;

    // Create a simple HTML report for PDF generation
    const reportContent = `
      <html>
        <head>
          <title>GEO Analysis Report - ${brandData?.company_name || 'Brand'}</title>
          <style>
            body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              margin: 20px;
              line-height: 1.6;
              color: #333;
            }
            .header {
              text-align: center;
              margin-bottom: 40px;
              padding-bottom: 20px;
              border-bottom: 2px solid #e5e7eb;
            }
            .score {
              font-size: 28px;
              font-weight: bold;
              color: #2563eb;
              margin-top: 15px;
            }
            .section {
              margin-bottom: 30px;
              page-break-inside: avoid;
            }
            .section h3 {
              color: #1f2937;
              border-bottom: 2px solid #e5e7eb;
              padding-bottom: 8px;
              margin-bottom: 15px;
            }
            .metric {
              display: inline-block;
              margin: 8px;
              padding: 12px;
              border: 1px solid #d1d5db;
              border-radius: 8px;
              background: #f9fafb;
              min-width: 200px;
            }
            .recommendation {
              margin: 15px 0;
              padding: 15px;
              border-left: 4px solid #2563eb;
              background: #f8fafc;
              border-radius: 6px;
              box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }
            .page {
              margin: 12px 0;
              padding: 12px;
              border: 1px solid #e5e7eb;
              border-radius: 6px;
              background: #fafafa;
            }
            @media print {
              .section { page-break-inside: avoid; }
              .recommendation { page-break-inside: avoid; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>GEO Report</h1>
            <h2>${brandData?.company_name || 'Brand'}</h2>
            <p>Generated on ${new Date().toLocaleDateString()}</p>
            <div class="score">Overall GEO Score: ${analysis.overallScore}/100</div>
          </div>

          <div class="section">
            <h3>Score Breakdown</h3>
            <div class="metric">Prompt Mention Rate: ${Math.round(analysis.promptMentionRate)}% (40% weight)</div>
            <div class="metric">Citation Rate: ${Math.round(analysis.citationRate)}% (20% weight)</div>
            <div class="metric">Website Optimization: ${analysis.websiteOptimization}/100 (30% weight)</div>
            <div class="metric">Sentiment/Tone: ${analysis.sentimentTone}/100 (10% weight)</div>
          </div>

          <div class="section">
            <h3>Analyzed Pages (${analysis.analyzedPages.length})</h3>
            ${analysis.analyzedPages.map(page => `
              <div class="page">
                <strong>${page.title}</strong> (Score: ${page.score}/100)<br>
                <small>${page.url}</small><br>
                ${page.summary}
              </div>
            `).join('')}
          </div>

          <div class="section">
            <h3>Actionable GEO Recommendations</h3>
            <p style="margin-bottom: 15px; color: #666; font-style: italic;">
              These AI-powered recommendations are specifically tailored to improve your brand's visibility in AI-generated responses.
            </p>
            ${analysis.recommendations.map((rec, index) => `
              <div class="recommendation" style="margin-bottom: 20px; page-break-inside: avoid;">
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                  <span style="background: ${rec.priority === 'high' ? '#dc2626' : rec.priority === 'medium' ? '#d97706' : '#16a34a'};
                               color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: bold; margin-right: 10px;">
                    ${rec.priority.toUpperCase()}
                  </span>
                  <span style="background: #f3f4f6; padding: 2px 8px; border-radius: 12px; font-size: 12px; color: #374151;">
                    ${rec.category}
                  </span>
                </div>
                <h4 style="margin: 8px 0; font-size: 16px; color: #1f2937;">${index + 1}. ${rec.title}</h4>
                <p style="margin: 8px 0; line-height: 1.5; color: #4b5563;">${rec.description}</p>
                <div style="background: #f0f9ff; padding: 10px; border-radius: 6px; border-left: 4px solid #0ea5e9; margin-top: 10px;">
                  <strong style="color: #0c4a6e;">Expected Impact:</strong> ${rec.impact}
                </div>
              </div>
            `).join('')}
          </div>

          <div class="section">
            <h3>AI Query Simulation</h3>
            ${analysis.simulatedQueries.map(query => `
              <div class="page">
                <strong>"${query.query}"</strong><br>
                Brand Mentioned: ${query.brand_mentioned ? 'Yes' : 'No'}<br>
                ${query.mention_context ? `Context: ${query.mention_context}<br>` : ''}
                Likelihood Score: ${query.likelihood_score}/100<br>
                ${query.reasoning}
              </div>
            `).join('')}
          </div>
        </body>
      </html>
    `;

    // Create a new window and print
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(reportContent);
      printWindow.document.close();
      printWindow.focus();
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 250);
    }
  };



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {currentView === 'historical-report' && (
            <Button
              onClick={() => setCurrentView('history')}
              variant="outline"
              size="sm"
              className="gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to History
            </Button>
          )}

          <div>
            <h2 className="text-2xl font-bold tracking-tight">
              {currentView === 'history' ? 'GEO Report History' :
               currentView === 'historical-report' ? 'Historical GEO Report' : 'Free GEO Report - Optimize for AI Citations'}
            </h2>
            <p className="text-muted-foreground text-sm">
              {currentView === 'history' ? 'View and compare your past GEO reports' :
               currentView === 'historical-report' ? 'Detailed view of historical analysis' :
               'AI search engines like ChatGPT and Claude are changing how people discover information. Get actionable insights to optimize your brand for AI citations rather than traditional search rankings, and discover specific steps to improve your visibility in AI-generated responses.'}
            </p>
          </div>
        </div>

        <div className="flex gap-2">
          {/* Navigation buttons */}
          {currentView !== 'historical-report' && (
            <>
              <Button
                onClick={() => setCurrentView(currentView === 'analysis' ? 'history' : 'analysis')}
                variant="outline"
                className="gap-2"
              >
                {currentView === 'analysis' ? (
                  <>
                    <History className="h-4 w-4" />
                    View History
                  </>
                ) : (
                  <>
                    <BarChart3 className="h-4 w-4" />
                    New Analysis
                  </>
                )}
              </Button>
            </>
          )}

          {/* Export button for current analysis */}
          {analysis && currentView === 'analysis' && (
            <Button
              onClick={exportToPDF}
              variant="outline"
              className="gap-2"
            >
              <Download className="h-4 w-4" />
              Export PDF
            </Button>
          )}

          {/* Run analysis button */}
          {currentView !== 'historical-report' && (
            <Button
              onClick={runGEOReport}
              disabled={loading || !brandData?.website}
              className="gap-2"
            >
              {loading ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
              {loading ? "Analyzing..." : "Run GEO Report"}
            </Button>
          )}
        </div>
      </div>

  

      {/* Historical Report View */}
      {currentView === 'historical-report' && selectedHistoricalAnalysis && (
        <GEOHistoricalReport
          analysis={selectedHistoricalAnalysis}
          onBack={() => setCurrentView('history')}
          onRunNewAnalysis={runGEOReport}
          loading={loading}
        />
      )}

      {/* History View */}
      {currentView === 'history' && (
        <GEOHistoryList
          history={history}
          loading={historyLoading}
          onViewReport={fetchHistoricalAnalysis}
          onLoadMore={() => fetchHistory(true)}
          hasMore={historyPagination.hasMore}
          currentAnalysis={analysis}
        />
      )}

      {/* Analysis View */}
      {currentView === 'analysis' && (
        <>
          {/* Loading State */}
          {loading && (
            <GEOAnalysisLoader
              isLoading={loading}
              brandName={brandData?.company_name}
              website={brandData?.website}
            />
          )}

          {!analysis && !loading && (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Search className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-xl font-semibold mb-3 text-center">Optimize Your Brand for AI Search Engines</h3>
                <div className="text-muted-foreground text-center mb-6 max-w-2xl space-y-3">
                  <p>
                    Traditional SEO optimizes for Google search rankings. <strong>GEO (Generative Engine Optimization)</strong> optimizes
                    for AI citations in ChatGPT, Claude, and other AI-powered responses.
                  </p>
                  <p>
                    Our free report examines how your brand appears in AI-generated responses and provides specific,
                    actionable recommendations to improve your visibility when people ask AI assistants about your industry.
                  </p>
                </div>
                {!brandData?.website && (
                  <div className="flex items-center gap-2 text-amber-600 mb-4">
                    <AlertCircle className="h-4 w-4" />
                    <span className="text-sm">Please add your website in brand settings first</span>
                  </div>
                )}
                <Button onClick={runGEOReport} disabled={!brandData?.website} className="gap-2 px-8 py-2">
                  <Search className="h-4 w-4" />
                  Start Free GEO Report
                </Button>
              </CardContent>
            </Card>
          )}
        </>
      )}

      {/* Report Results - Only show in analysis view */}
      {currentView === 'analysis' && analysis && (
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="pages">Pages Analyzed</TabsTrigger>
            <TabsTrigger value="queries">AI Queries</TabsTrigger>
            <TabsTrigger value="discoverability">AI Discoverability</TabsTrigger>
            <TabsTrigger value="content">Content Analysis</TabsTrigger>
            <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Overall Score */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Overall GEO Score
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-4">
                  <div className="text-4xl font-bold">
                    <span className={getScoreColor(analysis.overallScore)}>
                      {analysis.overallScore}
                    </span>
                    <span className="text-muted-foreground text-lg">/100</span>
                  </div>
                  <div className="flex-1">
                    <Progress value={analysis.overallScore} className="h-3" />
                  </div>
                  <Badge variant={getScoreBadgeVariant(analysis.overallScore)}>
                    {analysis.overallScore >= 80 ? "Excellent" : 
                     analysis.overallScore >= 60 ? "Good" : "Needs Improvement"}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Weighted Score Components */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <GEOScoreCard
                title="Prompt Mention Rate"
                score={Math.round(analysis.promptMentionRate)}
                description="40% weight - Brand appears in AI responses"
              />
              <GEOScoreCard
                title="Citation Rate"
                score={Math.round(analysis.citationRate)}
                description="20% weight - Website links in AI outputs"
              />
              <GEOScoreCard
                title="Website Optimization"
                score={analysis.websiteOptimization}
                description="30% weight - Content quality and structure"
              />
              <GEOScoreCard
                title="Sentiment/Tone"
                score={analysis.sentimentTone}
                description="10% weight - Positive brand framing"
              />
            </div>

            {/* Legacy Metrics for Compatibility */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <GEOScoreCard
                title="AI Discoverability"
                score={analysis.aiDiscoverability.score}
                description={`${analysis.aiDiscoverability.mentions} mentions found`}
              />
              <GEOScoreCard
                title="Content Optimization"
                score={analysis.contentOptimization.score}
                description="AI-friendly structure"
              />
              <GEOScoreCard
                title="Share of Voice"
                score={analysis.competitiveAnalysis.shareOfVoice}
                maxScore={100}
                description="vs competitors"
              />
            </div>
          </TabsContent>

          <TabsContent value="pages" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Analyzed Pages ({analysis.analyzedPages.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analysis.analyzedPages.map((page, index) => (
                    <div key={index} className="border rounded-lg p-4 space-y-2">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-sm">{page.title}</h4>
                        <Badge variant={page.score >= 70 ? "default" : page.score >= 50 ? "secondary" : "destructive"}>
                          {page.score}/100
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">{page.url}</p>
                      <p className="text-sm">{page.summary}</p>
                    </div>
                  ))}
                  {analysis.analyzedPages.length === 0 && (
                    <p className="text-muted-foreground text-center py-8">
                      No pages were analyzed. This might indicate issues with sitemap discovery or page accessibility.
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="queries" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>AI Query Simulation Results</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analysis.simulatedQueries.map((query, index) => (
                    <div key={index} className="border rounded-lg p-4 space-y-2">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-sm">&quot;{query.query}&quot;</h4>
                        <div className="flex items-center gap-2">
                          <Badge variant={query.brand_mentioned ? "default" : "outline"}>
                            {query.brand_mentioned ? "Mentioned" : "Not Mentioned"}
                          </Badge>
                          {query.mention_context && (
                            <Badge variant={
                              query.mention_context === "positive" ? "default" :
                              query.mention_context === "neutral" ? "secondary" : "destructive"
                            }>
                              {query.mention_context}
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Likelihood Score: {query.likelihood_score}/100
                      </div>
                      <p className="text-sm">{query.reasoning}</p>
                    </div>
                  ))}
                  {analysis.simulatedQueries.length === 0 && (
                    <p className="text-muted-foreground text-center py-8">
                      No queries were simulated. Please try running the analysis again.
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="discoverability" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    AI Mention Report
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Total Mentions</span>
                    <Badge variant="outline">{analysis.aiDiscoverability.mentions}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Sentiment</span>
                    <Badge variant={
                      analysis.aiDiscoverability.sentiment === "positive" ? "default" :
                      analysis.aiDiscoverability.sentiment === "neutral" ? "secondary" : "destructive"
                    }>
                      {analysis.aiDiscoverability.sentiment}
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Top Queries</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {analysis.aiDiscoverability.topQueries.map((query, index) => (
                      <div key={index} className="text-sm p-2 bg-muted rounded">
                        &quot;{query}&quot;
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="content" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <GEOScoreCard
                title="Structure Score"
                score={analysis.contentOptimization.structureScore}
                description="Heading hierarchy and organization"
              />
              <GEOScoreCard
                title="Factual Claims"
                score={analysis.contentOptimization.factualClaimsScore}
                description="Evidence-based content"
              />
              <GEOScoreCard
                title="AI Readability"
                score={analysis.contentOptimization.aiReadabilityScore}
                description="Optimized for AI consumption"
              />
            </div>
          </TabsContent>

          <TabsContent value="recommendations" className="space-y-4">
            {analysis.recommendations.map((rec, index) => (
              <GEORecommendationCard
                key={index}
                recommendation={rec}
              />
            ))}
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
